#pragma once

#include <atomic>
#include <chrono>
#include <functional>
#include <memory>
#include <mutex>
#include <queue>
#include <shared_mutex>
#include <unordered_map>
#include <thread>
#include <vector>

#include "DBTypes.h"
#include "DBPromise.h"

// 전방 선언
namespace NS
{
    struct Connection;
}

namespace Database
{

class NSDataSerializer;
class NSQueryData;
class NSMySQLConnection;
class ThreadedWorkManager;
class ConnectionManager;
class AsyncQueryExecutor;
struct AsyncQueryTask;

// 쿼리 작업 구조체 - 레거시 호환성 유지
struct QueryTask
{
    Connection connection;
    NSDataSerializer serializer;
    DBPromise<std::shared_ptr<NSQueryData>> promise;
    std::shared_ptr<NSQueryData> queryData;
    std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback;
    std::string procedureName;
    int64_t cid;
    std::function<void(NSMySQLConnection*, const NSDataSerializer&, std::shared_ptr<NSQueryData>)> executeFunc;
};

// RAII 패턴의 쿼리 타이머 - 자동으로 실행 시간 측정 및 콜백 호출
class QueryTimer
{
public:
    QueryTimer(std::shared_ptr<NSQueryData> data, 
               std::function<void(const std::shared_ptr<NSQueryData>&)> callback);
    ~QueryTimer();

    // 복사/이동 금지
    QueryTimer(const QueryTimer&) = delete;
    QueryTimer& operator=(const QueryTimer&) = delete;
    QueryTimer(QueryTimer&&) = delete;
    QueryTimer& operator=(QueryTimer&&) = delete;

private:
    std::chrono::high_resolution_clock::time_point m_start;
    std::shared_ptr<NSQueryData> m_queryData;
    std::function<void(const std::shared_ptr<NSQueryData>&)> m_callback;
};

// CID 기반 큐 관리자 - 각 CID별로 별도의 큐를 관리하여 순서 보장
class CIDQueueManager
{
public:
    CIDQueueManager();
    ~CIDQueueManager();

    void Initialize(ThreadedWorkManager* workerManager,
                   ConnectionManager* connectionManager,
                   std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback);

    void EnqueueQuery(int64_t cid, QueryTask task);

    size_t GetQueueCount() const;
    size_t GetQueueSize(int64_t cid) const;
    void GetQueueStats(std::unordered_map<int64_t, size_t>& stats) const;

private:
    // CID별 큐 구조체
    struct CIDQueue
    {
        std::queue<QueryTask> queries;        // 쿼리 큐
        mutable std::mutex mutex;              // 큐 보호용 뮤텍스
    };

    // 내부 메서드
    std::shared_ptr<CIDQueue> GetOrCreateQueue(int64_t cid);              // CID에 대한 큐 가져오기 또는 생성
    std::shared_ptr<CIDQueue> GetCIDQueue(int64_t cid);                   // CID 큐 가져오기
    void ProcessQueue(int64_t cid);                                       // 특정 CID의 큐 처리
    int64_t ExtractCIDFromSerializer(const NSDataSerializer& serializer); // Serializer에서 CID 추출
    
    // 비동기 처리 메서드
    void StartAsyncQuery(const QueryTask& task);
    void PollActiveQueries();
    void ProcessCompletedQuery(AsyncQueryTask& asyncTask);
    void CheckNextTaskForCID(int64_t cid);
    void PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData);

private:
    std::unordered_map<int64_t, std::shared_ptr<CIDQueue>> m_cidQueues;
    mutable std::shared_mutex m_mapMutex;
    
    ThreadedWorkManager* m_workerManager = nullptr;
    ConnectionManager* m_connectionManager = nullptr;
    std::function<void(const std::shared_ptr<NSQueryData>&)> m_afterExecuteCallback;
    
    // 통계
    std::atomic<uint64_t> m_totalQueriesProcessed{0};  // 처리된 쿼리 총 개수
    std::atomic<uint64_t> m_totalQueriesEnqueued{0};   // 등록된 쿼리 총 개수
    
    // 비동기 쿼리 실행기
    std::unique_ptr<AsyncQueryExecutor> m_asyncExecutor;
    
    // 진행 중인 비동기 작업들
    std::vector<AsyncQueryTask> m_activeQueries;
    std::mutex m_activeQueriesMutex;
    
    // 폴링 스레드
    std::thread m_pollingThread;
    std::atomic<bool> m_pollingRunning{false};
};

} // namespace Database