#include "stdafx.h"
#include "NSThread.h"
#include <stdexcept>

NSThread::NSThread(NSThread&& rhs) noexcept
{
	(void)operator=(std::move(rhs));
}

NSThread& NSThread::operator=(NSThread&& rhs) noexcept
{
	assert(!HasValidHandle());

	m_Handle = rhs.m_Handle;
	m_ThreadId = rhs.m_ThreadId;

	rhs.m_Handle = NULL;
	rhs.m_ThreadId = 0;

	return *this;
}

NSThread::NSThread(const std::function<void()>& function)
{
	auto* func = new std::function<void()>(function);
	m_Handle = ::CreateThread(
		NULL,
		NULL,
		NSThread_EntryPoint,
		func,
		NULL,
		&m_ThreadId
	);
	
	if (m_Handle == NULL)
	{
		delete func;
		throw std::runtime_error("Failed to create thread");
	}
}

NSThread::~NSThread()
{
	if (HasValidHandle())
	{
		if (IsRunning())
		{
			Wait();
		}

		::CloseHandle(m_Handle);
	}
}

bool NSThread::IsRunning() const
{
	if (!HasValidHandle())
		return false;

	return GetExitCode() == STILL_ACTIVE;
}

auto NSThread::GetExitCode() const -> DWORD
{
	DWORD exitCode = 0;
	[[maybe_unused]] BOOL result = ::GetExitCodeThread(m_Handle, &exitCode);
	assert(result);

	return exitCode;
}

void NSThread::Wait()
{
	(void)Wait(INFINITE);
}

bool NSThread::Wait(DWORD milliseconds)
{
	assert(HasValidHandle());
	DWORD result = ::WaitForSingleObject(m_Handle, milliseconds);

	return result == WAIT_OBJECT_0;
}

auto NSThread::GetThreadId() const -> DWORD
{
	assert(HasValidHandle());
	return m_ThreadId;
}

auto NSThread::GetPriority() const -> Priority
{
	assert(HasValidHandle());
	return static_cast<Priority>(::GetThreadPriority(m_Handle));
}

void NSThread::SetAffinity(Mask mask)
{
	assert(HasValidHandle());
	[[maybe_unused]] DWORD_PTR result = ::SetThreadAffinityMask(m_Handle, mask);
	assert(result);
}

void NSThread::SetPriority(Priority priority)
{
	assert(HasValidHandle());
	[[maybe_unused]] BOOL result = ::SetThreadPriority(m_Handle, static_cast<int>(priority));
	assert(result);
}

void NSThread::SetDescription(const std::string& description)
{
	std::wstring str(description.begin(), description.end());

	assert(HasValidHandle());
	[[maybe_unused]] HRESULT result = ::SetThreadDescription(m_Handle, str.c_str());
	assert(SUCCEEDED(result));
}

//bool NSThread::SetDescription(HANDLE handle, const wchar_t* description)
//{
//	return SUCCEEDED(::SetThreadDescription(handle, description));
//}

bool NSThread::HasValidHandle() const
{
	return m_Handle != NULL && m_Handle != INVALID_HANDLE_VALUE;
}

DWORD NSThread::NSThread_EntryPoint(LPVOID parameter)
{
	std::function<void()> userFunction;
	{
		auto fn = static_cast<std::function<void()>*>(parameter);
		userFunction.swap(*fn);
		delete fn;
	}

	userFunction();
	return EXIT_SUCCESS;
}