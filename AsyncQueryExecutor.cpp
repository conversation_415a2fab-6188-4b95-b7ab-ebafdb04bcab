#include "AsyncQueryExecutor.h"
#include "Connection/NSMySQLConnection.h"
#include "Diagnostics/NSLogger.h"
#include <winsock2.h>
#include <windows.h>

namespace Database
{

bool AsyncQueryExecutor::StartAsyncQuery(AsyncQueryTask& task)
{
    if (!task.connection)
    {
        task.errorMessage = "Connection is null";
        task.state = AsyncQueryState::Failed;
        return false;
    }

    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql)
    {
        task.errorMessage = "Failed to get raw MySQL connection";
        task.state = AsyncQueryState::Failed;
        return false;
    }
    
    task.startTime = std::chrono::steady_clock::now();
    
    // 타임아웃 체크
    if (IsTimedOut(task))
    {
        task.errorMessage = "Query timed out before start";
        task.state = AsyncQueryState::Failed;
        return false;
    }
    
    int status = 0;
    
    // 비동기 쿼리 시작
    if (task.isStmtQuery && task.stmt)
    {
        // PreparedStatement 비동기 실행
        status = mysql_stmt_execute_start(&task.asyncStatus, task.stmt);
        
        if (status == 0)
        {
            // 즉시 완료됨
            task.state = AsyncQueryState::WaitingResult;
            return true;
        }
        else if (task.asyncStatus != 0)
        {
            // 에러 발생
            SaveStmtError(task, task.stmt);
            task.state = AsyncQueryState::Failed;
            return false;
        }
    }
    else
    {
        // 일반 쿼리 비동기 실행
        status = mysql_real_query_start(&task.asyncStatus, mysql, 
                                       task.query.c_str(), 
                                       static_cast<unsigned long>(task.query.length()));
        
        if (status == 0)
        {
            // 즉시 완료됨
            task.state = AsyncQueryState::WaitingResult;
            return true;
        }
        else if (task.asyncStatus != 0)
        {
            // 에러 발생
            SaveError(task, mysql);
            task.state = AsyncQueryState::Failed;
            return false;
        }
    }
    
    // 비동기 실행 중
    task.state = AsyncQueryState::Executing;
    task.asyncStatus = status;
    return true;
}

bool AsyncQueryExecutor::ContinueAsyncQuery(AsyncQueryTask& task)
{
    if (!task.connection || task.state != AsyncQueryState::Executing)
    {
        return false;
    }

    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql)
    {
        task.errorMessage = "Lost MySQL connection during execution";
        task.state = AsyncQueryState::Failed;
        return false;
    }
    
    // 타임아웃 체크
    if (IsTimedOut(task))
    {
        task.errorMessage = "Query execution timed out";
        task.state = AsyncQueryState::Failed;
        return false;
    }
    
    int wait_status = GetWaitStatus(mysql);
    if (wait_status == 0)
    {
        // 아직 소켓이 준비되지 않음
        return false;
    }
    
    int status = 0;
    
    if (task.isStmtQuery && task.stmt)
    {
        status = mysql_stmt_execute_cont(&task.asyncStatus, task.stmt, wait_status);
        
        if (status == 0)
        {
            // 실행 완료
            if (task.asyncStatus != 0)
            {
                // 에러 발생
                SaveStmtError(task, task.stmt);
                task.state = AsyncQueryState::Failed;
                return false;
            }
            task.state = AsyncQueryState::WaitingResult;
            return true;
        }
    }
    else
    {
        status = mysql_real_query_cont(&task.asyncStatus, mysql, wait_status);
        
        if (status == 0)
        {
            // 실행 완료
            if (task.asyncStatus != 0)
            {
                // 에러 발생
                SaveError(task, mysql);
                task.state = AsyncQueryState::Failed;
                return false;
            }
            task.state = AsyncQueryState::WaitingResult;
            return true;
        }
    }
    
    // 계속 진행 중
    task.asyncStatus = status;
    return false;
}

bool AsyncQueryExecutor::FetchAsyncResult(AsyncQueryTask& task)
{
    if (!task.connection || task.state != AsyncQueryState::WaitingResult)
    {
        return false;
    }

    MYSQL* mysql = task.connection->GetRawConnection();
    if (!mysql)
    {
        task.errorMessage = "Lost MySQL connection while fetching result";
        task.state = AsyncQueryState::Failed;
        return false;
    }
    
    if (task.isStmtQuery && task.stmt)
    {
        // PreparedStatement 결과 저장
        int result = mysql_stmt_store_result(task.stmt);
        if (result == 0)
        {
            // 결과 메타데이터 가져오기
            task.result.reset(mysql_stmt_result_metadata(task.stmt));
            if (!task.result && mysql_stmt_field_count(task.stmt) > 0)
            {
                // 결과가 있어야 하는데 없음
                SaveStmtError(task, task.stmt);
                task.state = AsyncQueryState::Failed;
                return false;
            }
            task.state = AsyncQueryState::Completed;
            return true;
        }
        else
        {
            // store_result 실패
            SaveStmtError(task, task.stmt);
            task.state = AsyncQueryState::Failed;
            return false;
        }
    }
    else
    {
        // 일반 쿼리 결과 저장
        task.result.reset(mysql_store_result(mysql));
        
        // result가 NULL인 경우 체크
        if (!task.result)
        {
            // 결과가 없거나 에러
            if (mysql_field_count(mysql) == 0)
            {
                // UPDATE, INSERT 등 결과가 없는 쿼리
                task.state = AsyncQueryState::Completed;
                return true;
            }
            else
            {
                // 에러 발생
                SaveError(task, mysql);
                task.state = AsyncQueryState::Failed;
                return false;
            }
        }
        
        task.state = AsyncQueryState::Completed;
        return true;
    }
}

int AsyncQueryExecutor::GetWaitStatus(MYSQL* mysql)
{
    if (!mysql) return 0;

    // MySQL 소켓 가져오기
    my_socket socket_fd = mysql_get_socket(mysql);
    if (socket_fd == INVALID_SOCKET) 
    {
        LOGW << "Invalid socket from mysql_get_socket";
        return 0;
    }

    // Windows select() 사용
    fd_set read_fds, write_fds, except_fds;
    FD_ZERO(&read_fds);
    FD_ZERO(&write_fds);
    FD_ZERO(&except_fds);

    FD_SET(socket_fd, &read_fds);
    FD_SET(socket_fd, &write_fds);
    FD_SET(socket_fd, &except_fds);

    struct timeval timeout = {0, 0}; // 논블로킹 체크
    int result = select(static_cast<int>(socket_fd + 1), &read_fds, &write_fds, &except_fds, &timeout);

    if (result < 0)
    {
        int error = WSAGetLastError();
        LOGW << "select() failed with error: " << error;
        return 0;
    }

    int wait_status = 0;
    if (result > 0) 
    {
        if (FD_ISSET(socket_fd, &read_fds)) wait_status |= MYSQL_WAIT_READ;
        if (FD_ISSET(socket_fd, &write_fds)) wait_status |= MYSQL_WAIT_WRITE;
        if (FD_ISSET(socket_fd, &except_fds)) wait_status |= MYSQL_WAIT_EXCEPT;
    }

    return wait_status;
}

bool AsyncQueryExecutor::IsTimedOut(const AsyncQueryTask& task) const
{
    auto now = std::chrono::steady_clock::now();
    auto elapsed = std::chrono::duration_cast<std::chrono::milliseconds>(now - task.startTime);
    return elapsed >= task.timeout;
}

void AsyncQueryExecutor::CleanupTask(AsyncQueryTask& task)
{
    // STMT는 NSMySQLConnection의 캐시에서 가져온 것이므로 close하면 안됨
    // 단순히 참조만 해제
    task.stmt = nullptr;
    
    // 결과 정리 - unique_ptr가 자동으로 정리함
    task.result.reset();
    
    // 커넥션은 shared_ptr이므로 자동 정리됨
    task.connection.reset();
    
    // 상태 초기화
    task.state = AsyncQueryState::Pending;
    task.asyncStatus = 0;
    task.errorMessage.clear();
    task.errorCode = 0;
}

void AsyncQueryExecutor::SaveError(AsyncQueryTask& task, MYSQL* mysql)
{
    if (!mysql) 
    {
        task.errorMessage = "MySQL connection is null";
        task.errorCode = -1;
        return;
    }
    
    task.errorCode = mysql_errno(mysql);
    const char* error = mysql_error(mysql);
    if (error && *error)
    {
        task.errorMessage = error;
    }
    else
    {
        task.errorMessage = "Unknown MySQL error";
    }
    
    LOGE << "MySQL Error [" << task.errorCode << "]: " << task.errorMessage
         << " (Query: " << task.query.substr(0, 100) << "...)";
}

void AsyncQueryExecutor::SaveStmtError(AsyncQueryTask& task, MYSQL_STMT* stmt)
{
    if (!stmt)
    {
        task.errorMessage = "MySQL statement is null";
        task.errorCode = -1;
        return;
    }
    
    task.errorCode = mysql_stmt_errno(stmt);
    const char* error = mysql_stmt_error(stmt);
    if (error && *error)
    {
        task.errorMessage = error;
    }
    else
    {
        task.errorMessage = "Unknown MySQL statement error";
    }
    
    LOGE << "MySQL Statement Error [" << task.errorCode << "]: " << task.errorMessage
         << " (Procedure: " << task.procedureName << ")";
}

} // namespace Database