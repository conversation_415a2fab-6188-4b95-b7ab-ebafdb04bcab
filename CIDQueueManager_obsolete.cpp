#include "CIDQueueManager.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "NSMySQLConnection.h"
#include "Threading/ThreadedWorkManager.h"
#include "ConnectionManager.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "Diagnostics/NSLogger.h"
#include "NSDefine.h"
#include "AsyncQueryExecutor.h"
#include "GameThreadCallback.h"
#include "DBPerformanceMonitor.h"
#include "MySQLCommand.h"
#include <mysql.h>

namespace Database
{

QueryTimer::QueryTimer(std::shared_ptr<NSQueryData> data, 
                       std::function<void(const std::shared_ptr<NSQueryData>&)> callback)
    : m_start(std::chrono::high_resolution_clock::now())
    , m_queryData(std::move(data))
    , m_callback(std::move(callback))
{
}

QueryTimer::~QueryTimer()
{
    auto elapsed = std::chrono::high_resolution_clock::now() - m_start;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    if (m_queryData)
    {
        m_queryData->SetElapsedTime(elapsedMs);
        
        if (m_callback)
        {
            try
            {
                m_callback(m_queryData);
            }
            catch (const std::exception& e)
            {
                LOGE << "Exception in QueryTimer callback: " << e.what();
            }
        }
    }
}

CIDQueueManager::CIDQueueManager()
{
}

CIDQueueManager::~CIDQueueManager()
{
    // 폴링 스레드 종료
    m_pollingRunning = false;
    if (m_pollingThread.joinable())
    {
        m_pollingThread.join();
    }
    
    LOGD << "CIDQueueManager destroyed. Total queries processed: " << m_totalQueriesProcessed.load()
         << ", Total enqueued: " << m_totalQueriesEnqueued.load();
}

void CIDQueueManager::Initialize(ThreadedWorkManager* workerManager,
                                ConnectionManager* connectionManager,
                                std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback)
{
    m_workerManager = workerManager;
    m_connectionManager = connectionManager;
    m_afterExecuteCallback = std::move(afterExecuteCallback);
    
    m_asyncExecutor = std::make_unique<AsyncQueryExecutor>();
    
    // 폴링 스레드 시작
    m_pollingRunning = true;
    m_pollingThread = std::thread([this]() {
        mysql_thread_init(); // 스레드별 초기화
        
        while (m_pollingRunning)
        {
            PollActiveQueries();
            std::this_thread::sleep_for(std::chrono::milliseconds(1));
        }
        
        mysql_thread_end();
    });
}

void CIDQueueManager::EnqueueQuery(int64_t cid, QueryTask task)
{
    if (!m_workerManager || !m_connectionManager)
    {
        LOGE << "CIDQueueManager not properly initialized";
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("CIDQueueManager not initialized")));
        return;
    }

    task.cid = cid;
    m_totalQueriesEnqueued.fetch_add(1);

    auto cidQueue = GetOrCreateQueue(cid);
    
    bool wasEmpty = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        wasEmpty = cidQueue->queries.empty();
        cidQueue->queries.push(std::move(task));
    }
    
    // 큐가 비어있었으면 워커에 할당
    if (wasEmpty)
    {
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
}

std::shared_ptr<CIDQueueManager::CIDQueue> CIDQueueManager::GetOrCreateQueue(int64_t cid)
{
    {
        std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            return it->second;
        }
    }
    
    std::unique_lock<std::shared_mutex> writeLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        return it->second;
    }
    
    auto newQueue = std::make_shared<CIDQueue>();
    m_cidQueues[cid] = newQueue;
    return newQueue;
}

void CIDQueueManager::ProcessQueue(int64_t cid)
{
    auto cidQueue = GetCIDQueue(cid);
    if (!cidQueue)
    {
        LOGE << "CID queue not found for CID: " << cid;
        return;
    }
    
    while (true)
    {
        QueryTask task;
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            if (cidQueue->queries.empty())
            {
                break;
            }
            
            task = std::move(cidQueue->queries.front());
            cidQueue->queries.pop();
        }
        
        // 비동기로 실행
        StartAsyncQuery(task);
        
        // 즉시 다음 CID 처리로 이동 (블로킹하지 않음!)
        return;
    }
}

int64_t CIDQueueManager::ExtractCIDFromSerializer(const NSDataSerializer& serializer)
{
    // This is a placeholder - actual implementation would extract CID from serializer
    // based on the specific protocol/format used
    // For now, assuming CID is the first int64_t parameter
    int64_t cid = 0;
    // serializer.Read(cid); // Pseudo-code
    return cid;
}

size_t CIDQueueManager::GetQueueCount() const
{
    std::shared_lock<std::shared_mutex> lock(m_mapMutex);
    return m_cidQueues.size();
}

size_t CIDQueueManager::GetQueueSize(int64_t cid) const
{
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        std::lock_guard<std::mutex> queueLock(it->second->mutex);
        return it->second->queries.size();
    }
    return 0;
}

void CIDQueueManager::GetQueueStats(std::unordered_map<int64_t, size_t>& stats) const
{
    stats.clear();
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    
    for (const auto& [cid, queue] : m_cidQueues)
    {
        std::lock_guard<std::mutex> queueLock(queue->mutex);
        if (!queue->queries.empty())
        {
            stats[cid] = queue->queries.size();
        }
    }
}

std::shared_ptr<CIDQueueManager::CIDQueue> CIDQueueManager::GetCIDQueue(int64_t cid)
{
    std::shared_lock<std::shared_mutex> readLock(m_mapMutex);
    auto it = m_cidQueues.find(cid);
    if (it != m_cidQueues.end())
    {
        return it->second;
    }
    return nullptr;
}

void CIDQueueManager::StartAsyncQuery(const QueryTask& task)
{
    auto conn = m_connectionManager->GetAvailableConnection();
    if (!conn)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get available connection")));
        CheckNextTaskForCID(task.cid);
        return;
    }
    
    AsyncQueryTask asyncTask;
    asyncTask.cid = task.cid;
    asyncTask.connection = conn;
    asyncTask.procedureName = task.procedureName;
    
    // 타이머 생성 (RAII로 자동 측정)
    auto timer = std::make_shared<QueryTimer>(task.queryData, m_afterExecuteCallback);
    
    // 성능 모니터링 시작
    DBPerformanceMonitor::GetInstance().RecordQueryStart(task.cid, 
        task.executeFunc ? "CALL " + task.procedureName + "(...)" : "CALL " + task.procedureName + "()");
    
    asyncTask.callback = [this, task, conn, timer](bool success, MYSQL_RES* result) {
        // 성능 모니터링 완료
        DBPerformanceMonitor::GetInstance().RecordQueryComplete(task.cid, success);
        
        // 타이머 소멸 시 자동으로 elapsed time 설정 및 콜백 호출
        timer.reset();
        
        if (success)
        {
            // RecordSet 생성 및 promise 완료
            task.queryData->SetResult(result);
            task.promise.SetValue(task.queryData);
            
            // 게임 스레드로 결과 전달
            PostToGameThread(task.cid, task.queryData);
        }
        else
        {
            task.promise.SetException(std::make_exception_ptr(
                std::runtime_error("Query execution failed")));
        }
        
        // 커넥션 해제
        m_connectionManager->ReleaseConnection(conn);
        
        // 다음 작업 확인
        CheckNextTaskForCID(task.cid);
    };
    
    // 쿼리 실행 준비
    if (task.executeFunc)
    {
        // NSMySQLConnection의 기존 PreparedStatement 캐싱 시스템 활용
        // 프로시저 메타데이터 가져오기 (이미 캐싱됨)
        auto metadata = conn->GetProcedureMetadata(task.procedureName);
        if (!metadata)
        {
            // 메타데이터가 없으면 로드 시도
            if (!conn->LoadProcedureMetadata(task.procedureName))
            {
                task.promise.SetException(std::make_exception_ptr(
                    std::runtime_error("Failed to load procedure metadata for " + task.procedureName)));
                m_connectionManager->ReleaseConnection(conn);
                CheckNextTaskForCID(task.cid);
                return;
            }
            metadata = conn->GetProcedureMetadata(task.procedureName);
        }
        
        // CALL 쿼리 생성
        std::stringstream ss;
        ss << "CALL " << task.procedureName << "(";
        for (size_t i = 0; i < metadata->parameters.size(); ++i)
        {
            if (i > 0) ss << ", ";
            ss << "?";
        }
        ss << ")";
        std::string callQuery = ss.str();
        
        // 캐시된 PreparedStatement 가져오기
        MYSQL_STMT* stmt = conn->GetCachedStatement(callQuery);
        if (!stmt)
        {
            task.promise.SetException(std::make_exception_ptr(
                std::runtime_error("Failed to get cached statement for " + task.procedureName)));
            m_connectionManager->ReleaseConnection(conn);
            CheckNextTaskForCID(task.cid);
            return;
        }
        
        // MySQLCommand를 사용하여 파라미터 바인딩
        auto command = std::make_unique<MySQLCommand>();
        
        // NSStoredProcedure의 MakeQuery를 호출해야 하지만
        // 현재는 간단히 빈 command로 진행 (실제로는 StoredProcedure 인스턴스가 필요)
        std::vector<MYSQL_BIND> binds;
        command->BindToMySQL(stmt, binds);
        
        asyncTask.isStmtQuery = true;
        asyncTask.stmt = stmt;
        asyncTask.query = callQuery;
        asyncTask.procedureName = task.procedureName;
        asyncTask.parameterCount = static_cast<int>(metadata->parameters.size());
    }
    else
    {
        // 일반 쿼리 실행 (프로시저 직접 호출)
        asyncTask.query = "CALL " + task.procedureName + "()";
    }
    
    // 비동기 실행 시작
    if (m_asyncExecutor->StartAsyncQuery(asyncTask))
    {
        if (asyncTask.state == AsyncQueryState::WaitingResult || 
            asyncTask.state == AsyncQueryState::Completed ||
            asyncTask.state == AsyncQueryState::Failed)
        {
            // 즉시 완료된 경우
            ProcessCompletedQuery(asyncTask);
            // stmt는 NSMySQLConnection이 관리하므로 별도 정리 불필요
        }
        else
        {
            // 진행 중인 경우
            std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
            m_activeQueries.push_back(std::move(asyncTask));
        }
    }
    else
    {
        // 시작 실패
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error(asyncTask.errorMessage.empty() ? "Failed to start async query" : asyncTask.errorMessage)));
        m_connectionManager->ReleaseConnection(conn);
        // stmt는 GetCachedStatement로 가져온 것이므로 close하면 안됨
        // NSMySQLConnection이 관리함
        CheckNextTaskForCID(task.cid);
    }
}

void CIDQueueManager::PollActiveQueries()
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    
    for (auto it = m_activeQueries.begin(); it != m_activeQueries.end();)
    {
        auto& task = *it;
        
        // 타임아웃 체크
        if (m_asyncExecutor->IsTimedOut(task))
        {
            LOGW << "Query timed out for CID " << task.cid << ", procedure: " << task.procedureName;
            task.state = AsyncQueryState::Failed;
            task.errorMessage = "Query execution timed out";
            ProcessCompletedQuery(task);
            m_asyncExecutor->CleanupTask(task);
            it = m_activeQueries.erase(it);
            continue;
        }
        
        switch (task.state)
        {
        case AsyncQueryState::Executing:
            if (m_asyncExecutor->ContinueAsyncQuery(task))
            {
                // 상태가 변경됨 - WaitingResult로 전환됨
                if (task.state == AsyncQueryState::WaitingResult)
                {
                    // 바로 결과 가져오기 시도
                    if (m_asyncExecutor->FetchAsyncResult(task))
                    {
                        ProcessCompletedQuery(task);
                        m_asyncExecutor->CleanupTask(task);
                        it = m_activeQueries.erase(it);
                        continue;
                    }
                }
                else if (task.state == AsyncQueryState::Failed)
                {
                    ProcessCompletedQuery(task);
                    m_asyncExecutor->CleanupTask(task);
                    it = m_activeQueries.erase(it);
                    continue;
                }
            }
            break;
            
        case AsyncQueryState::WaitingResult:
            if (m_asyncExecutor->FetchAsyncResult(task))
            {
                // 완료 처리
                ProcessCompletedQuery(task);
                m_asyncExecutor->CleanupTask(task);
                it = m_activeQueries.erase(it);
                continue;
            }
            break;
            
        case AsyncQueryState::Completed:
        case AsyncQueryState::Failed:
            ProcessCompletedQuery(task);
            m_asyncExecutor->CleanupTask(task);
            it = m_activeQueries.erase(it);
            continue;
        }
        
        ++it;
    }
}

void CIDQueueManager::ProcessCompletedQuery(AsyncQueryTask& asyncTask)
{
    m_totalQueriesProcessed.fetch_add(1);
    
    if (asyncTask.callback)
    {
        bool success = (asyncTask.state == AsyncQueryState::Completed);
        
        // 디버그 로깅
        if (!success)
        {
            LOGE << "Query failed for CID " << asyncTask.cid 
                 << ", procedure: " << asyncTask.procedureName
                 << ", error: " << asyncTask.errorMessage;
        }
        
        try
        {
            asyncTask.callback(success, asyncTask.result);
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception in query callback for CID " << asyncTask.cid << ": " << e.what();
        }
    }
    
    // 결과는 콜백에서 처리했으므로 여기서는 정리하지 않음
    // asyncTask.result는 콜백에서 NSQueryData에 이동됨
}

void CIDQueueManager::CheckNextTaskForCID(int64_t cid)
{
    auto cidQueue = GetCIDQueue(cid);
    if (!cidQueue)
        return;
    
    // 콜백에서만 pop하므로 경합 없음
    std::lock_guard<std::mutex> lock(cidQueue->mutex);
    if (!cidQueue->queries.empty())
    {
        // 다음 작업을 워커에 할당
        m_workerManager->PostWork([this, cid]() {
            ProcessQueue(cid);
        });
    }
}

void CIDQueueManager::PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData)
{
    // 게임 스레드로 결과 전달
    GameThreadCallback::PostToGameThread(nullptr, [this, queryData]() {
        if (m_afterExecuteCallback)
        {
            m_afterExecuteCallback(queryData);
        }
    });
}

} // namespace Database