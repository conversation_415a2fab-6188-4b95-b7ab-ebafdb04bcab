#pragma once

// Clang Thread Safety Analysis Annotations
// 컴파일 타임에 스레드 안전성 문제를 검출하는 어노테이션들

#ifdef __clang__
#define THREAD_ANNOTATION_ATTRIBUTE__(x) __attribute__((x))
#else
#define THREAD_ANNOTATION_ATTRIBUTE__(x) // no-op for non-Clang compilers
#endif

// 기본 어노테이션들
#define CAPABILITY(x) THREAD_ANNOTATION_ATTRIBUTE__(capability(x))
#define SCOPED_CAPABILITY THREAD_ANNOTATION_ATTRIBUTE__(scoped_lockable)

// 데이터 보호 어노테이션
#define GUARDED_BY(x) THREAD_ANNOTATION_ATTRIBUTE__(guarded_by(x))
#define PT_GUARDED_BY(x) THREAD_ANNOTATION_ATTRIBUTE__(pt_guarded_by(x))

// 락 순서 정의 (데드락 방지)
#define ACQUIRED_BEFORE(...) THREAD_ANNOTATION_ATTRIBUTE__(acquired_before(__VA_ARGS__))
#define ACQUIRED_AFTER(...) THREAD_ANNOTATION_ATTRIBUTE__(acquired_after(__VA_ARGS__))

// 함수 요구사항 어노테이션
#define REQUIRES(...) THREAD_ANNOTATION_ATTRIBUTE__(requires_capability(__VA_ARGS__))
#define REQUIRES_SHARED(...) THREAD_ANNOTATION_ATTRIBUTE__(requires_shared_capability(__VA_ARGS__))

// 락 획득/해제 어노테이션
#define ACQUIRE(...) THREAD_ANNOTATION_ATTRIBUTE__(acquire_capability(__VA_ARGS__))
#define ACQUIRE_SHARED(...) THREAD_ANNOTATION_ATTRIBUTE__(acquire_shared_capability(__VA_ARGS__))
#define RELEASE(...) THREAD_ANNOTATION_ATTRIBUTE__(release_capability(__VA_ARGS__))
#define RELEASE_SHARED(...) THREAD_ANNOTATION_ATTRIBUTE__(release_shared_capability(__VA_ARGS__))

// 조건부 락 획득
#define TRY_ACQUIRE(...) THREAD_ANNOTATION_ATTRIBUTE__(try_acquire_capability(__VA_ARGS__))
#define TRY_ACQUIRE_SHARED(...) THREAD_ANNOTATION_ATTRIBUTE__(try_acquire_shared_capability(__VA_ARGS__))

// 락 제외 어노테이션
#define EXCLUDES(...) THREAD_ANNOTATION_ATTRIBUTE__(locks_excluded(__VA_ARGS__))

// 어설션 어노테이션
#define ASSERT_CAPABILITY(x) THREAD_ANNOTATION_ATTRIBUTE__(assert_capability(x))
#define ASSERT_SHARED_CAPABILITY(x) THREAD_ANNOTATION_ATTRIBUTE__(assert_shared_capability(x))

// 기타 어노테이션
#define RETURN_CAPABILITY(x) THREAD_ANNOTATION_ATTRIBUTE__(lock_returned(x))
#define NO_THREAD_SAFETY_ANALYSIS THREAD_ANNOTATION_ATTRIBUTE__(no_thread_safety_analysis)

// RAII 락 가드 어노테이션
template<typename MutexType>
class SCOPED_CAPABILITY AnnotatedLockGuard {
public:
    explicit AnnotatedLockGuard(MutexType& mu) ACQUIRE(mu) : mutex_(mu) {
        mutex_.lock();
    }
    
    ~AnnotatedLockGuard() RELEASE() {
        mutex_.unlock();
    }
    
private:
    MutexType& mutex_;
};

// 사용 예시:
// class MyClass {
// private:
//     int data_ GUARDED_BY(mutex_);
//     std::mutex mutex_;
// 
// public:
//     void SetData(int value) ACQUIRE(mutex_) RELEASE(mutex_) {
//         AnnotatedLockGuard<std::mutex> lock(mutex_);
//         data_ = value;
//     }
// 
//     int GetData() REQUIRES(mutex_) {
//         return data_;  // 호출자가 이미 락을 잡고 있어야 함
//     }
// };
