#include "stdafx.h"
#include "NSPQueryData.h"

#include "Database/NSMySQLCommand.h"
#include "Database/NSMySQLRecordset.h"
#include "QueryData/NSDataSerializer.h"

NSPQueryData::NSPQueryData()
{
	m_pcDataSerializer = std::make_unique<NSDataSerializer>();

	for (int i = 0; i < g_uMaxQueryRecordSetCount; i++)
	{
		m_iReturnValue[i] = -100;
		// m_pcRecordset[i]와 m_pcCommand[i]는 기본적으로 nullptr로 초기화됨
	}

	m_pcRecordset[0] = std::make_unique<NSMySQLRecordset>(); //초기에는 하나만 생성해둔다.
	m_eErrorCode = EErrorCode::None;
	m_iQuerySetCount = 0;
	m_mapRecordsetByName.clear();
	m_mapReturnValueByName.clear();
	m_mapCommandNameByIdx.clear();

	m_pAllocFuncName = nullptr;
	m_iAllocLine = 0;
}

NSPQueryData::~NSPQueryData()
{
	// unique_ptr이 자동으로 메모리를 관리하므로 명시적 delete 불필요
}

void NSPQueryData::Reset()
{
	m_eErrorCode = EErrorCode::None;
	m_iQuerySetCount = 0;
	
	// 모든 command를 reset
	for (int i = 0; i < g_uMaxQueryRecordSetCount; i++)
	{
		m_pcCommand[i].reset();
	}
	
	for (int i = 0; i < g_uMaxQueryRecordSetCount; i++)
	{
		m_iReturnValue[i] = -100;
		if (m_pcRecordset[i])
		{
			m_pcRecordset[i]->Reset();
			if (i > 0)
			{
				//하나만 두고 모두 삭제한다.
				m_pcRecordset[i].reset();
			}
		}
	}

	m_pcDataSerializer->Clear();
	m_mapRecordsetByName.clear();
	m_mapReturnValueByName.clear();

	m_pAllocFuncName = nullptr;
	m_iAllocLine = 0;
}

NSDataSerializer& NSPQueryData::GetQueryData()
{
	return (*m_pcDataSerializer);
}

NSMySQLRecordset* NSPQueryData::GetRecordSet()
{
	return m_pcRecordset[0].get();
}

NSMySQLRecordset* NSPQueryData::GetRecordSet(int iIndex)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return nullptr;

	return m_pcRecordset[iIndex].get();
}

NSMySQLRecordset* NSPQueryData::GetRecordSet(const char* strCommandName)
{
	if (strCommandName == nullptr)
		return nullptr;

	MAP_RECORDSET::iterator it = m_mapRecordsetByName.find(strCommandName);
	if (it == m_mapRecordsetByName.end())
		return nullptr;

	return it->second;
}

bool NSPQueryData::SetCommand(std::unique_ptr<NSMySQLCommand> pcCommand)
{
	if (pcCommand == nullptr)
		return false;

	if (m_iQuerySetCount >= g_uMaxQueryRecordSetCount)
	{
		LOGE << "AdoRecordset buffer Over!!";
		return false;
	}

	m_pcCommand[m_iQuerySetCount] = std::move(pcCommand);
	if (!m_pcRecordset[m_iQuerySetCount])
	{
		m_pcRecordset[m_iQuerySetCount] = std::make_unique<NSMySQLRecordset>();
	}

	++m_iQuerySetCount;
	return true;
}

void NSPQueryData::SetRecordSet(std::string strCommandName, NSMySQLRecordset* pcRecordSet)
{
	MAP_RECORDSET::iterator it = m_mapRecordsetByName.find(strCommandName);
	if (it != m_mapRecordsetByName.end())
		return;

	m_mapRecordsetByName.insert(std::make_pair(strCommandName, pcRecordSet));
}

void NSPQueryData::SetReturnValue(int iIndex, std::string strCommandName, int iReturnValue)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return;

	m_iReturnValue[iIndex] = iReturnValue;

	{
		MAP_RETURNVALUE::iterator it = m_mapReturnValueByName.find(strCommandName);
		if (it == m_mapReturnValueByName.end())
		{
			m_mapReturnValueByName.insert(std::make_pair(strCommandName, iReturnValue));
		}
	}

	{
		MAP_COMMANDNAME::iterator it = m_mapCommandNameByIdx.find(iIndex);
		if (it == m_mapCommandNameByIdx.end())
		{
			m_mapCommandNameByIdx.insert(std::make_pair(iIndex, strCommandName));
		}
	}
}

NSMySQLCommand* NSPQueryData::GetCommand(int iIndex)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return nullptr;

	return m_pcCommand[iIndex].get();
}

const char* NSPQueryData::GetCommandName(int iIndex)
{
	MAP_COMMANDNAME::iterator it = m_mapCommandNameByIdx.find(iIndex);
	if (it == m_mapCommandNameByIdx.end())
	{
		return "";
	}

	return it->second.c_str();
}

int NSPQueryData::GetReturnValue()
{
	return m_iReturnValue[0];
}

int NSPQueryData::GetReturnValue(int iIndex)
{
	if (iIndex < 0 || iIndex >= m_iQuerySetCount)
		return -100;

	return m_iReturnValue[iIndex];
}

int NSPQueryData::GetReturnValue(const char* strCommandName)
{
	MAP_RETURNVALUE::iterator it = m_mapReturnValueByName.find(strCommandName);
	if (it == m_mapReturnValueByName.end())
		return -100;

	return it->second;
}

void NSPQueryData::SetErrorCode(const EErrorCode eErrorCode)
{
	m_eErrorCode = eErrorCode;
}

EErrorCode NSPQueryData::GetErrorCode() const
{
	return m_eErrorCode;
}

bool NSPQueryData::IsValid() const
{
	return GetErrorCode() == EErrorCode::None;
}

void NSPQueryData::SetElapsedTime(std::chrono::high_resolution_clock::duration elapsedTime)
{
	m_ElapsedTime = elapsedTime;
}

auto NSPQueryData::GetElapsedTime() const->std::chrono::high_resolution_clock::duration
{
	return m_ElapsedTime;
}

