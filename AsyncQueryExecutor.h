#pragma once
#include <functional>
#include <mysql.h>
#include <atomic>
#include <chrono>
#include <memory>
#include <string>
#include "mimalloc_integration.h"

namespace Database
{

class NSMySQLConnection;

// 비동기 쿼리 상태
enum class AsyncQueryState
{
    Pending,
    Executing,
    WaitingResult,
    Completed,
    Failed
};

// 비동기 쿼리 작업
struct AsyncQueryTask
{
    int64_t cid;
    std::string query;
    std::shared_ptr<NSMySQLConnection> connection;
    std::function<void(bool success, MYSQL_RES* result)> callback;
    
    AsyncQueryState state = AsyncQueryState::Pending;
    int asyncStatus = 0;
    std::chrono::steady_clock::time_point startTime;
    
    // 비동기 실행 컨텍스트
    bool isStmtQuery = false;
    MYSQL_STMT* stmt = nullptr;  // 캐시에서 빌려온 것이므로 raw pointer 유지
    std::unique_ptr<MYSQL_RES, decltype(&mysql_free_result)> result{nullptr, mysql_free_result};
    
    // 타임아웃 설정 (기본 30초)
    std::chrono::milliseconds timeout{30000};
    
    // 에러 정보
    std::string errorMessage;
    int errorCode = 0;
    
    // 프로시저 정보
    std::string procedureName;
    int parameterCount = 0;
};

class AsyncQueryExecutor
{
public:
    AsyncQueryExecutor() = default;
    ~AsyncQueryExecutor() = default;

    // 비동기 쿼리 시작
    bool StartAsyncQuery(AsyncQueryTask& task);
    
    // 진행 중인 쿼리 상태 확인 및 진행
    bool ContinueAsyncQuery(AsyncQueryTask& task);
    
    // 결과 가져오기
    bool FetchAsyncResult(AsyncQueryTask& task);
    
    // 타임아웃 체크
    bool IsTimedOut(const AsyncQueryTask& task) const;
    
    // 리소스 정리
    void CleanupTask(AsyncQueryTask& task);
    
private:
    // 소켓 대기 상태 확인
    int GetWaitStatus(MYSQL* mysql);
    
    // 에러 정보 저장
    void SaveError(AsyncQueryTask& task, MYSQL* mysql);
    void SaveStmtError(AsyncQueryTask& task, MYSQL_STMT* stmt);
};

} // namespace Database