#pragma once
#include <vector>
#include <memory>
#include <functional>
#include <thread>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <atomic>
#include <string>

class ThreadedWorkManager
{
public:
    using WorkFunction = std::function<void()>;

    ThreadedWorkManager();
    ~ThreadedWorkManager();

    // 초기화/종료
    bool Initialize(int workerCount);
    void Finalize();

    // 작업 등록 - threadIndex를 받아서 특정 스레드에 작업 할당
    void PostWork(WorkFunction work, int threadIndex);

    // 기존 API 호환성을 위한 메서드 (랜덤 스레드에 할당)
    void PostWork(WorkFunction work);

    // 통계 정보
    int64_t GetQueueSize(int threadIndex) const;
    int64_t GetTotalQueueSize() const;

private:
    struct WorkerThread
    {
        std::string name;
        std::thread thread;
        std::queue<WorkFunction> queue;
        mutable std::mutex queueMutex;
        std::condition_variable cv;
        std::atomic<bool> running{true};
        std::atomic<int64_t> queueSize{0};
        std::atomic<int64_t> processedCount{0};
    };

    std::vector<std::unique_ptr<WorkerThread>> m_workers;
    std::atomic<bool> m_initialized{false};
    std::atomic<int> m_nextWorkerIndex{0};

    void WorkerProc(int index);
    void SetThreadName(const std::string& name);
};