#pragma once
#include <atomic>
#include <chrono>
#include <unordered_map>
#include <mutex>
#include <string>
#include <memory>

namespace Database
{

// 단일 인스턴스 성능 모니터
class DBPerformanceMonitor
{
public:
    static DBPerformanceMonitor& GetInstance();
    
    // 쿼리 시작 기록
    void RecordQueryStart(int64_t cid, const std::string& query);
    
    // 쿼리 완료 기록
    void RecordQueryComplete(int64_t cid, bool success);
    
    // 통계 출력
    void LogStatistics();
    
    // 통계 초기화
    void ResetStatistics();
    
    // 통계 정보 구조체
    struct Statistics
    {
        uint64_t totalQueries = 0;
        uint64_t successfulQueries = 0;
        uint64_t failedQueries = 0;
        uint64_t totalTimeMs = 0;
        uint64_t minTimeMs = UINT64_MAX;
        uint64_t maxTimeMs = 0;
        double avgTimeMs = 0.0;
        uint64_t activeQueries = 0;
    };
    
    // 전체 통계 가져오기
    Statistics GetGlobalStatistics() const;
    
    // 프로시저별 통계 가져오기
    void GetProcedureStatistics(std::unordered_map<std::string, Statistics>& stats) const;
    
private:
    DBPerformanceMonitor() = default;
    ~DBPerformanceMonitor() = default;
    
    // 싱글톤을 위한 복사/이동 금지
    DBPerformanceMonitor(const DBPerformanceMonitor&) = delete;
    DBPerformanceMonitor& operator=(const DBPerformanceMonitor&) = delete;
    
    struct QueryStats
    {
        std::atomic<uint64_t> count{0};
        std::atomic<uint64_t> totalTimeMs{0};
        std::atomic<uint64_t> failures{0};
        std::atomic<uint64_t> minTimeMs{UINT64_MAX};
        std::atomic<uint64_t> maxTimeMs{0};
    };
    
    // 프로시저별 통계
    std::unordered_map<std::string, std::unique_ptr<QueryStats>> m_queryStats;
    mutable std::mutex m_statsMutex;
    
    // 활성 쿼리 추적
    struct ActiveQuery
    {
        std::string procedureName;
        std::chrono::steady_clock::time_point startTime;
    };
    
    std::unordered_map<int64_t, std::unique_ptr<ActiveQuery>> m_activeQueries;
    mutable std::mutex m_activeQueriesMutex;
    
    // 전역 통계
    std::atomic<uint64_t> m_totalQueries{0};
    std::atomic<uint64_t> m_successfulQueries{0};
    std::atomic<uint64_t> m_failedQueries{0};
    std::atomic<uint64_t> m_totalTimeMs{0};
    
    // 마지막 로그 시간
    std::chrono::steady_clock::time_point m_lastLogTime;
    
    // 프로시저 이름 추출
    std::string ExtractProcedureName(const std::string& query) const;
    
    // 통계 업데이트
    void UpdateStatistics(const std::string& procedureName, uint64_t elapsedMs, bool success);
};

} // namespace Database