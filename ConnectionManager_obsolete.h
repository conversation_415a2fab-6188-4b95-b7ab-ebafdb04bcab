#pragma once

#include <atomic>
#include <memory>
#include <vector>
#include <string>
#include <mutex>

namespace Database
{

class NSMySQLConnection;
struct ConnectionInfo;

// Connection 래퍼 - 스레드 안전성을 위한 구조체
struct ConnectionWrapper
{
    std::shared_ptr<NSMySQLConnection> connection;
    mutable std::mutex mutex;  // 커넥션별 뮤텍스
    std::atomic<bool> inUse{false};
    std::atomic<uint64_t> useCount{0};  // 사용 횟수 추적
    
    // 커넥션 해제
    void Release()
    {
        inUse.store(false);
    }
    
    // 커넥션 획득
    bool Acquire()
    {
        bool expected = false;
        return inUse.compare_exchange_strong(expected, true);
    }
};

// 라운드 로빈 방식의 연결 관리자
class ConnectionManager
{
public:
    ConnectionManager();
    ~ConnectionManager();

    bool Initialize(const ConnectionInfo& connInfo, int connectionCount);
    void Shutdown();

    std::shared_ptr<NSMySQLConnection> GetNextConnection();
    std::shared_ptr<NSMySQLConnection> GetAvailableConnection();
    void ReleaseConnection(std::shared_ptr<NSMySQLConnection> conn);
    
    size_t GetConnectionCount() const { return m_connections.size(); }
    bool IsInitialized() const { return m_initialized; }
    
    // 통계
    uint64_t GetTotalConnectionsUsed() const { return m_totalConnectionsUsed.load(); }
    void GetConnectionStats(std::vector<std::pair<size_t, uint64_t>>& stats) const;

private:
    std::vector<std::unique_ptr<ConnectionWrapper>> m_connections;
    std::atomic<size_t> m_nextConnection{0};
    std::atomic<uint64_t> m_totalConnectionsUsed{0};
    std::atomic<bool> m_initialized{false};
    
    // 연결 생성을 위한 연결 정보
    std::string m_host;
    uint16_t m_port;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    std::string m_charset;
    uint32_t m_connectionFlags;
    
    // 커넥션 찾기 헬퍼
    ConnectionWrapper* FindWrapperByConnection(const std::shared_ptr<NSMySQLConnection>& conn);
};

} // namespace Database