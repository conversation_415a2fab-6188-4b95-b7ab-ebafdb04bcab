#include "NSDataBaseManager.h"
#include "Connection/NSMySQLConnectionPool.h"
#include "Connection/NSMySQLConnection.h"
#include "StoredProcedure/NSStoredProcedure.h"
#include "NSDataSerializer.h"
#include "NSQueryData.h"
#include "Storage/NSStorageUpdateContainer.h"
#include "Storage/NSStorageManager.h"
#include "NSDBSession.h"
#include "AsyncQueryExecutor.h"
#include "DBPerformanceMonitor.h"
#include "MySQLCommand.h"
#include <thread>
#include <sstream>
#include <mysql.h>
#include "Diagnostics/NSLogger.h"

// 스레드 로컬 시퀀스 스토리지 정의
thread_local NSDataBaseManager::ThreadLocalSequence NSDataBaseManager::t_localSequence;

namespace Database
{

QueryTimer::QueryTimer(std::shared_ptr<NSQueryData> data, 
                       std::function<void(const std::shared_ptr<NSQueryData>&)> callback)
    : m_start(std::chrono::high_resolution_clock::now())
    , m_queryData(std::move(data))
    , m_callback(std::move(callback))
{
}

QueryTimer::~QueryTimer()
{
    auto elapsed = std::chrono::high_resolution_clock::now() - m_start;
    auto elapsedMs = std::chrono::duration_cast<std::chrono::milliseconds>(elapsed).count();
    
    if (m_queryData)
    {
        m_queryData->SetElapsedTime(elapsedMs);
        
        if (m_callback)
        {
            try
            {
                m_callback(m_queryData);
            }
            catch (const std::exception& e)
            {
                LOGE << "Exception in QueryTimer callback: " << e.what();
            }
        }
    }
}

} // namespace Database

NSDataBaseManager::NSDataBaseManager()
{
    // MariaDB 라이브러리 초기화 (프로세스당 1회)
    if (mysql_library_init(0, NULL, NULL) != 0)
    {
        LOGE << "Failed to initialize MySQL library";
        throw std::runtime_error("MySQL library initialization failed");
    }
}

NSDataBaseManager::~NSDataBaseManager()
{
    Finalize();
    
    // MariaDB 라이브러리 정리
    mysql_library_end();
}

bool NSDataBaseManager::Initialize()
{
    if (m_initialized.exchange(true))
        return true;

    try
    {
        // 비동기 쿼리 실행기 생성
        m_asyncExecutor = std::make_unique<Database::AsyncQueryExecutor>();
        
        // 연결 풀은 AddConnectionInfo에서 생성됨

        return true;
    }
    catch (const std::exception& e)
    {
        m_initialized = false;
        return false;
    }
}

void NSDataBaseManager::Finalize()
{
    if (!m_initialized.exchange(false))
        return;

    Stop();

    // 폴링 스레드 종료
    m_pollingRunning = false;
    if (m_pollingThread.joinable())
    {
        m_pollingThread.join();
    }

    // 연결 풀 종료
    for (auto& pool : m_connectionPools)
    {
        if (pool)
        {
            pool->Finalize();
            pool.reset();
        }
    }
}

bool NSDataBaseManager::AddConnectionInfo(int32 dbType, const std::string& host, int port,
                                         const std::string& dbName, const std::string& user, const std::string& password)
{
    auto* pool = GetConnectionPool(dbType);
    if (!pool)
        return false;
        
    return pool->AddConnectionInfo(host, port, dbName, user, password);
}

bool NSDataBaseManager::AddConnectionInfo(EDataBase dbType, EDBProvider provider, const std::string_view host, 
                                         uint32_t port, const std::string_view dbName, const std::string_view user, 
                                         const std::string_view password, int32_t initPoolCount)
{
    // provider 파라미터는 무시 (항상 MariaDB 사용)
    
    int32 dbTypeInt = static_cast<int32>(dbType);
    
    // 범위 체크
    if (dbTypeInt < static_cast<int32>(EDataBase::Game) || dbTypeInt >= static_cast<int32>(EDataBase::End))
        return false;
    
    // 기존 풀이 없으면 파라미터로 생성
    if (!m_connectionPools[dbTypeInt])
    {
        m_connectionPools[dbTypeInt] = std::make_unique<NSMySQLConnectionPool>(
            std::string(host), port, std::string(dbName), 
            std::string(user), std::string(password));
    }
    
    auto* pool = m_connectionPools[dbTypeInt].get();
    if (!pool)
        return false;
        
    // 풀 크기 설정
    pool->SetMaxConnections(initPoolCount);
    pool->SetMinConnections(std::min(5, initPoolCount));
    
    // 풀 초기화
    if (!pool->Initialize(dbTypeInt, 0))
        return false;
    
    return true;
}

NSMySQLConnectionPool* NSDataBaseManager::GetDBConnection(int32 dbType)
{
    return GetConnectionPool(dbType);
}

void NSDataBaseManager::ReconnectConnection(int32 dbType)
{
    auto* pool = GetConnectionPool(dbType);
    if (pool)
    {
        pool->Reconnect();
    }
}

int64_t NSDataBaseManager::GetDBQueueSize() const
{
    std::lock_guard<std::mutex> lock(m_workQueueMutex);
    return m_workQueue.size();
}

std::string NSDataBaseManager::GetConnectionPoolCountInfo() const
{
    std::stringstream ss;
    for (int i = 0; i < 12; ++i)
    {
        if (m_connectionPools[i])
        {
            ss << "Pool[" << i << "]: " << m_connectionPools[i]->GetActiveConnectionCount() 
               << "/" << m_connectionPools[i]->GetTotalConnectionCount() << " ";
        }
    }
    return ss.str();
}

std::string NSDataBaseManager::GetConnectionPoolCountLog() const
{
    return GetConnectionPoolCountInfo(); // 동일한 정보 반환
}

NSMySQLConnectionPool* NSDataBaseManager::GetConnectionPool(int32 dbType)
{
    if (dbType >= static_cast<int32>(EDataBase::Game) && dbType < static_cast<int32>(EDataBase::End))
        return m_connectionPools[dbType].get();

    return nullptr;
}

// 시퀀스 관리 메서드 구현 (단순화)
int64_t NSDataBaseManager::GetNextStorageSequence(int64_t cid)
{
    // 스레드 로컬 스토리지 사용 - 락 불필요
    return ++t_localSequence.sequenceByCid[cid];
}

void NSDataBaseManager::OnSessionClosed(int64_t cid)
{
    // 스레드 로컬 스토리지에서 제거 - 락 불필요
    t_localSequence.sequenceByCid.erase(cid);
}

template<typename SP>
DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl(
    const Connection& connection,
    const NSDataSerializer& serializer,
    int64_t transactionId)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        // CID 추출
        int64_t cid = 0;
        if constexpr (requires { SP::Input; })
        {
            NSDataSerializer tempSerializer = serializer;
            SP::Input input;
            tempSerializer >> input;
            if constexpr (requires { input.Cid; })
                cid = input.Cid;
            else if constexpr (requires { input.Aid; })
                cid = input.Aid;
        }
        
        m_queriesProcessing++;
        m_inputCount++;
        
        // QueryData 생성
        auto queryData = std::make_shared<NSQueryData>();
        queryData->SetProcedureName(SP::GetProcedureName());
        
        // QueryTask 생성
        Database::QueryTask task{
            .connection = connection,
            .serializer = serializer,
            .promise = promise,
            .queryData = queryData,
            .afterExecuteCallback = m_afterExecuteQueryShared,
            .procedureName = SP::GetProcedureName(),
            .cid = cid,
            .executeFunc = [](NSMySQLConnection* conn, const NSDataSerializer& ser, std::shared_ptr<NSQueryData> data) {
                SP sp;
                auto result = sp.Execute(conn, ser);
                data->SetErrorCode(result);
            }
        };
        
        // CID 큐에 추가
        EnqueueQuery(cid, std::move(task));
        
        // Note: The actual query execution is now handled by CIDQueueManager
        // The promise will be fulfilled when the query is processed
    });
}

bool NSDataBaseManager::Start(uint32_t workThreadCnt)
{
    if (!m_initialized)
        return false;
        
    // 스레드 수 결정
    if (workThreadCnt == 0)
    {
        // 전체 연결 수 계산
        int totalConnections = 0;
        for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
        {
            if (m_connectionPools[i])
            {
                totalConnections += m_connectionPools[i]->GetMaxConnections();
            }
        }
        
        // 연결갯수 나누기 2 (설계 문서대로)
        workThreadCnt = std::max(totalConnections / 2, 32);
    }
    
    m_threadCount = workThreadCnt;
    m_running = true;
    
    // 워커 스레드 생성
    m_workerThreads.reserve(workThreadCnt);
    for (uint32_t i = 0; i < workThreadCnt; ++i)
    {
        m_workerThreads.emplace_back([this]() {
            WorkerThreadFunc();
        });
    }
    
    // 폴링 스레드 시작
    m_pollingRunning = true;
    m_pollingThread = std::thread([this]() {
        mysql_thread_init(); // 스레드별 초기화
        
        while (m_pollingRunning)
        {
            PollActiveQueries();
            
            // CPU 사용률 개선: 활성 쿼리가 없으면 더 길게 대기
            std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
            if (m_activeQueries.empty())
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(10));
            }
            else
            {
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
            }
        }
        
        mysql_thread_end();
    });
        
    // 연결 풀은 AddConnectionInfo에서 이미 초기화됨
    // 여기서는 모든 풀이 준비되었는지 확인만 함
    for (int i = static_cast<int>(EDataBase::Game); i < static_cast<int>(EDataBase::End); ++i)
    {
        if (m_connectionPools[i] && !m_connectionPools[i]->GetActiveConnections())
        {
            LOGW << "Connection pool " << i << " has no active connections";
        }
    }
        
    return true;
}

void NSDataBaseManager::Stop()
{
    ProhibitPushAccess();
    StopAllWorkerThreadAndWait();
}

void NSDataBaseManager::ProhibitPushAccess()
{
    m_pushAccessProhibit = true;
}

void NSDataBaseManager::StopAllWorkerThreadAndWait()
{
    // 모든 큐 작업이 완료될 때까지 대기
    while (m_pushAccessCount > 0 || m_queriesProcessing > 0)
    {
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    // 워커 스레드 종료
    m_running = false;
    m_workCV.notify_all();
    
    for (auto& thread : m_workerThreads)
    {
        if (thread.joinable())
        {
            thread.join();
        }
    }
    m_workerThreads.clear();
}

DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StorageUpdateQuery(
    std::shared_ptr<NSStorageUpdateContainer> containerData,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
    std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
    std::shared_ptr<NSDBSession> session,
    std::source_location location)
{
    // Access 체크
    if (m_pushAccessProhibit.load())
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::CreateRejected(
            std::make_exception_ptr(std::runtime_error("Push access prohibited")));
    }
    
    m_pushAccessCount++;
    
    return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
        auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
        
        // CID 기반 스레드 선택
        int threadIndex = GetExecutorByShardKey(containerData->Cid);
        
        m_queriesProcessing++;
        
        // 작업 큐에 추가
        PostWork([=]() mutable
        {
            // RAII 가드로 atomic counter 보장
            struct CounterGuard {
                std::atomic<int>& queries;
                std::atomic<int>& access;
                ~CounterGuard() {
                    queries--;
                    access--;
                }
            } guard{m_queriesProcessing, m_pushAccessCount};
            
            try {
                // Query 실행
                EErrorCode queryResult = EErrorCode::None;
                if (pQueryFunc)
                {
                    queryResult = pQueryFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // Result 처리
                if (pResultFunc)
                {
                    queryResult = pResultFunc(queryData, containerData);
                    queryData->SetErrorCode(queryResult);
                }
                
                // 콜백 실행
                if (m_afterExecuteQuery)
                    m_afterExecuteQuery(*queryData);
                if (m_afterExecuteQueryShared)
                    m_afterExecuteQueryShared(queryData);
                
                promise.SetValue(queryData);
            }
            catch (const std::exception& e) {
                LOGE << "StorageUpdateQuery exception: " << e.what() 
                     << " at " << location.function_name() << ":" << location.line();
                promise.SetException(std::current_exception());
            }
            catch (...) {
                LOGE << "Unknown exception in StorageUpdateQuery"
                     << " at " << location.function_name() << ":" << location.line();
                promise.SetException(std::current_exception());
            }
        });
    });
}

// CID Queue 관리 메소드 구현
void NSDataBaseManager::EnqueueQuery(int64_t cid, Database::QueryTask task)
{
    task.cid = cid;
    
    // CID별 큐 가져오기 또는 생성 (맵 락은 짧게)
    std::shared_ptr<CIDQueue> cidQueue;
    {
        std::shared_lock<std::shared_mutex> readLock(m_cidQueuesMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            cidQueue = it->second;
        }
    }
    
    if (!cidQueue)
    {
        std::unique_lock<std::shared_mutex> writeLock(m_cidQueuesMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            cidQueue = it->second;
        }
        else
        {
            cidQueue = std::make_shared<CIDQueue>();
            m_cidQueues[cid] = cidQueue;
        }
    }
    
    // CID별 락으로 큐 작업 (다른 CID와 경합 없음)
    bool needWorker = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        bool wasEmpty = cidQueue->tasks.empty();
        cidQueue->tasks.push(std::move(task));
        
        if (wasEmpty)
        {
            bool expected = false;
            needWorker = cidQueue->processing.compare_exchange_strong(expected, true);
        }
    }
    
    // 락 밖에서 워커 할당
    if (needWorker)
    {
        PostWork([this, cid]() {
            ProcessCIDQueue(cid);
        });
    }
}

void NSDataBaseManager::ProcessCIDQueue(int64_t cid)
{
    // CID 큐 찾기 (맵 락은 한 번만)
    std::shared_ptr<CIDQueue> cidQueue;
    {
        std::shared_lock<std::shared_mutex> lock(m_cidQueuesMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            cidQueue = it->second;
        }
    }
    
    if (!cidQueue)
    {
        LOGE << "CID queue not found for CID: " << cid;
        return;
    }
    
    // CID별 락으로 작업 처리
    while (true)
    {
        Database::QueryTask task;
        bool hasTask = false;
        
        {
            std::lock_guard<std::mutex> lock(cidQueue->mutex);
            if (!cidQueue->tasks.empty())
            {
                task = std::move(cidQueue->tasks.front());
                cidQueue->tasks.pop();
                hasTask = true;
            }
            else
            {
                cidQueue->processing = false;
            }
        }
        
        if (!hasTask)
            break;
        
        // 비동기로 실행 (락 밖에서)
        StartAsyncQuery(task);
        
        // 즉시 다음 CID 처리로 이동 (블로킹하지 않음!)
        return;
    }
}

void NSDataBaseManager::StartAsyncQuery(const Database::QueryTask& task)
{
    // 연결 풀에서 가용한 연결 가져오기
    auto* pool = GetConnectionPool(task.connection.GetDBType());
    if (!pool)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get connection pool")));
        CheckNextTaskForCID(task.cid);
        return;
    }
    
    auto conn = pool->GetConnection();
    if (!conn)
    {
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error("Failed to get available connection")));
        CheckNextTaskForCID(task.cid);
        return;
    }
    
    Database::AsyncQueryTask asyncTask;
    asyncTask.cid = task.cid;
    asyncTask.connection = conn;
    asyncTask.procedureName = task.procedureName;
    
    // 타이머 생성 (RAII로 자동 측정)
    auto timer = std::make_shared<Database::QueryTimer>(task.queryData, m_afterExecuteQueryShared);
    
    // 성능 모니터링 시작
    DBPerformanceMonitor::GetInstance().RecordQueryStart(task.cid, 
        task.executeFunc ? "CALL " + task.procedureName + "(...)" : "CALL " + task.procedureName + "()");
    
    asyncTask.callback = [this, task, conn, pool, timer](bool success, MYSQL_RES* result) {
        // 성능 모니터링 완료
        DBPerformanceMonitor::GetInstance().RecordQueryComplete(task.cid, success);
        
        // 타이머 소멸 시 자동으로 elapsed time 설정 및 콜백 호출
        timer.reset();
        
        if (success)
        {
            // RecordSet 생성
            task.queryData->SetResult(result);
            
            // 게임 스레드로 전달하여 promise 완료
            if (m_gameThreadPost)
            {
                m_gameThreadPost([task]() mutable {
                    // 게임 스레드에서 promise 완료 → Then 콜백도 게임 스레드에서 실행
                    task.promise.SetValue(task.queryData);
                });
            }
            else
            {
                // 디스패처 없으면 현재 스레드에서 실행
                task.promise.SetValue(task.queryData);
            }
        }
        else
        {
            // 에러도 게임 스레드에서 처리
            if (m_gameThreadPost)
            {
                m_gameThreadPost([task]() mutable {
                    task.promise.SetException(std::make_exception_ptr(
                        std::runtime_error("Query execution failed")));
                });
            }
            else
            {
                task.promise.SetException(std::make_exception_ptr(
                    std::runtime_error("Query execution failed")));
            }
        }
        
        // 커넥션 반환
        pool->ReturnConnection(std::move(conn));
        
        // 다음 작업 확인
        CheckNextTaskForCID(task.cid);
    };
    
    // 쿼리 실행 준비
    if (task.executeFunc)
    {
        // NSMySQLConnection의 기존 PreparedStatement 캐싱 시스템 활용
        // 프로시저 메타데이터 가져오기 (이미 캐싱됨)
        auto metadata = conn->GetProcedureMetadata(task.procedureName);
        if (!metadata)
        {
            // 메타데이터가 없으면 로드 시도
            if (!conn->LoadProcedureMetadata(task.procedureName))
            {
                task.promise.SetException(std::make_exception_ptr(
                    std::runtime_error("Failed to load procedure metadata for " + task.procedureName)));
                pool->ReturnConnection(std::move(conn));
                CheckNextTaskForCID(task.cid);
                return;
            }
            metadata = conn->GetProcedureMetadata(task.procedureName);
        }
        
        // CALL 쿼리 생성
        std::stringstream ss;
        ss << "CALL " << task.procedureName << "(";
        for (size_t i = 0; i < metadata->parameters.size(); ++i)
        {
            if (i > 0) ss << ", ";
            ss << "?";
        }
        ss << ")";
        std::string callQuery = ss.str();
        
        // 캐시된 PreparedStatement 가져오기
        MYSQL_STMT* stmt = conn->GetCachedStatement(callQuery);
        if (!stmt)
        {
            task.promise.SetException(std::make_exception_ptr(
                std::runtime_error("Failed to get cached statement for " + task.procedureName)));
            pool->ReturnConnection(std::move(conn));
            CheckNextTaskForCID(task.cid);
            return;
        }
        
        // MySQLCommand를 사용하여 파라미터 바인딩
        auto command = std::make_unique<MySQLCommand>();
        
        // NSStoredProcedure의 MakeQuery를 호출해야 하지만
        // 현재는 간단히 빈 command로 진행 (실제로는 StoredProcedure 인스턴스가 필요)
        std::vector<MYSQL_BIND> binds;
        command->BindToMySQL(stmt, binds);
        
        asyncTask.isStmtQuery = true;
        asyncTask.stmt = stmt;
        asyncTask.query = callQuery;
        asyncTask.procedureName = task.procedureName;
        asyncTask.parameterCount = static_cast<int>(metadata->parameters.size());
    }
    else
    {
        // 일반 쿼리 실행 (프로시저 직접 호출)
        asyncTask.query = "CALL " + task.procedureName + "()";
    }
    
    // 비동기 실행 시작
    if (m_asyncExecutor->StartAsyncQuery(asyncTask))
    {
        if (asyncTask.state == Database::AsyncQueryState::WaitingResult || 
            asyncTask.state == Database::AsyncQueryState::Completed ||
            asyncTask.state == Database::AsyncQueryState::Failed)
        {
            // 즉시 완료된 경우
            ProcessCompletedQuery(asyncTask);
            // stmt는 NSMySQLConnection이 관리하므로 별도 정리 불필요
        }
        else
        {
            // 진행 중인 경우
            std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
            m_activeQueries.push_back(std::move(asyncTask));
        }
    }
    else
    {
        // 시작 실패
        task.promise.SetException(std::make_exception_ptr(
            std::runtime_error(asyncTask.errorMessage.empty() ? "Failed to start async query" : asyncTask.errorMessage)));
        pool->ReturnConnection(std::move(conn));
        // stmt는 GetCachedStatement로 가져온 것이므로 close하면 안됨
        // NSMySQLConnection이 관리함
        CheckNextTaskForCID(task.cid);
    }
}

void NSDataBaseManager::PollActiveQueries()
{
    std::lock_guard<std::mutex> lock(m_activeQueriesMutex);
    
    for (auto it = m_activeQueries.begin(); it != m_activeQueries.end();)
    {
        auto& task = *it;
        
        // 타임아웃 체크
        if (m_asyncExecutor->IsTimedOut(task))
        {
            LOGW << "Query timed out for CID " << task.cid << ", procedure: " << task.procedureName;
            task.state = Database::AsyncQueryState::Failed;
            task.errorMessage = "Query execution timed out";
            ProcessCompletedQuery(task);
            m_asyncExecutor->CleanupTask(task);
            it = m_activeQueries.erase(it);
            continue;
        }
        
        switch (task.state)
        {
        case Database::AsyncQueryState::Executing:
            if (m_asyncExecutor->ContinueAsyncQuery(task))
            {
                // 상태가 변경됨 - WaitingResult로 전환됨
                if (task.state == Database::AsyncQueryState::WaitingResult)
                {
                    // 바로 결과 가져오기 시도
                    if (m_asyncExecutor->FetchAsyncResult(task))
                    {
                        ProcessCompletedQuery(task);
                        m_asyncExecutor->CleanupTask(task);
                        it = m_activeQueries.erase(it);
                        continue;
                    }
                }
                else if (task.state == Database::AsyncQueryState::Failed)
                {
                    ProcessCompletedQuery(task);
                    m_asyncExecutor->CleanupTask(task);
                    it = m_activeQueries.erase(it);
                    continue;
                }
            }
            break;
            
        case Database::AsyncQueryState::WaitingResult:
            if (m_asyncExecutor->FetchAsyncResult(task))
            {
                // 완료 처리
                ProcessCompletedQuery(task);
                m_asyncExecutor->CleanupTask(task);
                it = m_activeQueries.erase(it);
                continue;
            }
            break;
            
        case Database::AsyncQueryState::Completed:
        case Database::AsyncQueryState::Failed:
            ProcessCompletedQuery(task);
            m_asyncExecutor->CleanupTask(task);
            it = m_activeQueries.erase(it);
            continue;
        }
        
        ++it;
    }
}

void NSDataBaseManager::ProcessCompletedQuery(Database::AsyncQueryTask& asyncTask)
{
    m_outputCount++;
    m_queriesProcessing--;
    
    if (asyncTask.callback)
    {
        bool success = (asyncTask.state == Database::AsyncQueryState::Completed);
        
        // 디버그 로깅
        if (!success)
        {
            LOGE << "Query failed for CID " << asyncTask.cid 
                 << ", procedure: " << asyncTask.procedureName
                 << ", error: " << asyncTask.errorMessage;
        }
        
        try
        {
            asyncTask.callback(success, asyncTask.result);
        }
        catch (const std::exception& e)
        {
            LOGE << "Exception in query callback for CID " << asyncTask.cid << ": " << e.what();
        }
    }
    
    // 결과는 콜백에서 처리했으므로 여기서는 정리하지 않음
    // asyncTask.result는 콜백에서 NSQueryData에 이동됨
}

void NSDataBaseManager::CheckNextTaskForCID(int64_t cid)
{
    // CID 큐 찾기
    std::shared_ptr<CIDQueue> cidQueue;
    {
        std::shared_lock<std::shared_mutex> lock(m_cidQueuesMutex);
        auto it = m_cidQueues.find(cid);
        if (it != m_cidQueues.end())
        {
            cidQueue = it->second;
        }
    }
    
    if (!cidQueue)
        return;
    
    // CID별 락으로 확인
    bool needWorker = false;
    {
        std::lock_guard<std::mutex> lock(cidQueue->mutex);
        if (!cidQueue->tasks.empty())
        {
            bool expected = false;
            needWorker = cidQueue->processing.compare_exchange_strong(expected, true);
        }
    }
    
    // 락 밖에서 워커 할당
    if (needWorker)
    {
        PostWork([this, cid]() {
            ProcessCIDQueue(cid);
        });
    }
}

void NSDataBaseManager::PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData)
{
    // 더 이상 사용되지 않음 - promise.SetValue()가 게임 스레드에서 실행되므로
    // Then 콜백이 자동으로 게임 스레드에서 실행됨
}

void NSDataBaseManager::PostWork(std::function<void()> work)
{
    {
        std::lock_guard<std::mutex> lock(m_workQueueMutex);
        m_workQueue.push(std::move(work));
    }
    m_workCV.notify_one();
}

void NSDataBaseManager::WorkerThreadFunc()
{
    mysql_thread_init(); // MariaDB 스레드 초기화
    
    while (m_running)
    {
        std::function<void()> work;
        {
            std::unique_lock<std::mutex> lock(m_workQueueMutex);
            m_workCV.wait(lock, [this] { return !m_workQueue.empty() || !m_running; });
            
            if (!m_running)
                break;
                
            if (!m_workQueue.empty())
            {
                work = std::move(m_workQueue.front());
                m_workQueue.pop();
            }
        }
        
        if (work)
        {
            try
            {
                work();
            }
            catch (const std::exception& e)
            {
                LOGE << "Exception in worker thread: " << e.what();
            }
        }
    }
    
    mysql_thread_end(); // MariaDB 스레드 정리
}

// Explicit template instantiations (if needed)
// template DBPromise<std::shared_ptr<NSQueryData>> NSDataBaseManager::StartQueryImpl<SomeSpecificProcedure>(...);