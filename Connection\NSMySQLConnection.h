#pragma once
#include <mysql.h>
#include <string>
#include <memory>
#include <unordered_map>
#include <mutex>
#include <chrono>
#include <list>
#include <atomic>
#include "../mimalloc_integration.h"

// Forward declarations
class MySQLCommand;
class RecordSet;

// MariaDB 연결 클래스 - 메모리 관리 개선 버전
class NSMySQLConnection
{
public:
    NSMySQLConnection();
    ~NSMySQLConnection();

    // 복사/이동 방지
    NSMySQLConnection(const NSMySQLConnection&) = delete;
    NSMySQLConnection& operator=(const NSMySQLConnection&) = delete;
    NSMySQLConnection(NSMySQLConnection&&) = delete;
    NSMySQLConnection& operator=(NSMySQLConnection&&) = delete;

    // 연결 관리
    bool Connect(const char* host, int port, const char* user, 
                 const char* password, const char* database);
    void Disconnect();
    bool Reconnect();
    bool IsConnected() const;
    bool CheckConnection();  // 실제 DB 호출 시점에 연결 확인 및 재연결

    // 프로시저 실행
    bool ExecuteProcedure(const std::string& procName, MySQLCommand* command);
    
    // 쿼리 실행
    bool ExecuteQuery(const std::string& query);
    
    // 결과 가져오기
    std::unique_ptr<RecordSet> GetRecordSet();
    
    // 트랜잭션
    bool BeginTransaction();
    bool CommitTransaction();
    bool RollbackTransaction();

    // 논블로킹 실행 지원
    bool ExecuteProcedureAsync(const std::string& procName, MySQLCommand* command);
    bool PollAsyncResult();

    // MySQL 핸들 접근
    MYSQL* GetHandle() { return m_mysql; }
    MYSQL* GetRawConnection() { return m_mysql; }

    // 에러 정보
    const char* GetLastError() const;
    int GetLastErrorCode() const;

private:
    // 프로시저 메타데이터 캐싱
    struct ProcedureMetadata
    {
        struct Parameter
        {
            mi::string name;  // mimalloc 사용
            enum_field_types type;
            bool isOutput;
        };
        mi::vector<Parameter> parameters;  // mimalloc 사용
        bool hasResultSet;
    };
    
    bool LoadProcedureMetadata(const std::string& procName);
    std::shared_ptr<ProcedureMetadata> GetProcedureMetadata(const std::string& procName);

    // PreparedStatement 캐시 관리
    struct StmtCacheEntry
    {
        std::unique_ptr<MYSQL_STMT, decltype(&mysql_stmt_close)> stmt;
        std::string query;
        std::chrono::steady_clock::time_point lastUsed;
        std::list<std::string>::iterator lruIter;
        
        StmtCacheEntry() : stmt(nullptr, mysql_stmt_close) {}
    };
    
    void EvictOldestStatement();
    MYSQL_STMT* GetCachedStatement(const std::string& query);
    void ClearStatementCache();

    // MYSQL 커스텀 삭제자
    struct MysqlDeleter {
        void operator()(MYSQL* mysql) {
            if (mysql) {
                mysql_close(mysql);
            }
        }
    };

    // MYSQL_RES 커스텀 삭제자
    struct MysqlResDeleter {
        void operator()(MYSQL_RES* res) {
            if (res) {
                mysql_free_result(res);
            }
        }
    };

private:
    // MySQL 연결 (smart pointer 사용)
    std::unique_ptr<MYSQL, MysqlDeleter> m_mysql;
    
    // 현재 실행 중인 statement (캐시에서 관리)
    MYSQL_STMT* m_currentStmt = nullptr;
    
    // 현재 결과셋 (smart pointer 사용)
    std::unique_ptr<MYSQL_RES, MysqlResDeleter> m_result;
    
    // 연결 정보
    std::string m_host;
    int m_port = 3306;
    std::string m_user;
    std::string m_password;
    std::string m_database;
    
    // 프로시저 메타데이터 캐시 (shared_ptr 사용)
    mi::unordered_map<std::string, std::shared_ptr<ProcedureMetadata>> m_metadataCache;  // mimalloc 사용
    mutable std::mutex m_metadataMutex;
    
    // PreparedStatement 캐시 (LRU)
    mi::unordered_map<std::string, StmtCacheEntry> m_stmtCache;  // mimalloc 사용
    mi::list<std::string> m_lruList; // LRU 순서 관리, mimalloc 사용
    mutable std::mutex m_stmtCacheMutex;
    static constexpr size_t MAX_STMT_CACHE_SIZE = 100;
    
    // 비동기 실행 상태
    std::atomic<bool> m_asyncExecuting{false};
    std::atomic<int> m_asyncStatus{0};
    
    // 연결 상태 (thread-safe)
    std::atomic<bool> m_connected{false};
};