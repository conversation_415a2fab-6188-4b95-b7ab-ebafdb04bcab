#pragma once
#include "NSDefine.h"
// #include "NSSingleton.h" // 게임서버에서 제공
#include "DBPromise.h"
#include "mimalloc_integration.h"
#include <memory>
#include <type_traits>
#include <atomic>
#include <functional>
#include <vector>
#include <optional>
#include <source_location>
#include <queue>
#include <unordered_map>
#include <shared_mutex>
#include <thread>
#include <condition_variable>

// Forward declaration for EDataBase enum (defined elsewhere)
enum class EDataBase;
enum class EDBProvider;

// Forward declarations
class NSDataSerializer;
class NSQueryData;
class NSStoredProcedure;
class NSMySQLConnectionPool;
class NSMySQLConnection;
struct NSStorageUpdateContainer;

// Connection은 게임서버에서 정의됨
struct Connection;

// Database namespace
namespace Database
{
    class AsyncQueryExecutor;
    struct AsyncQueryTask;
    
    // 쿼리 작업 구조체
    struct QueryTask
    {
        Connection connection;
        NSDataSerializer serializer;
        DBPromise<std::shared_ptr<NSQueryData>> promise;
        std::shared_ptr<NSQueryData> queryData;
        std::function<void(const std::shared_ptr<NSQueryData>&)> afterExecuteCallback;
        std::string procedureName;
        int64_t cid;
        std::function<void(NSMySQLConnection*, const NSDataSerializer&, std::shared_ptr<NSQueryData>)> executeFunc;
    };
    
    // 쿼리 타이머 (RAII)
    class QueryTimer
    {
    public:
        QueryTimer(std::shared_ptr<NSQueryData> data, 
                   std::function<void(const std::shared_ptr<NSQueryData>&)> callback);
        ~QueryTimer();
        
        QueryTimer(const QueryTimer&) = delete;
        QueryTimer& operator=(const QueryTimer&) = delete;
        QueryTimer(QueryTimer&&) = delete;
        QueryTimer& operator=(QueryTimer&&) = delete;
        
    private:
        std::chrono::high_resolution_clock::time_point m_start;
        std::shared_ptr<NSQueryData> m_queryData;
        std::function<void(const std::shared_ptr<NSQueryData>&)> m_callback;
    };
}

// 심플하고 레거시 호환되는 데이터베이스 매니저
class NSDataBaseManager : public TemplateSingleton<NSDataBaseManager>
{
public:
    NSDataBaseManager();
    ~NSDataBaseManager();

public:
    // 초기화
    bool Initialize();
    void Finalize();
    
    // 워커 스레드 관리
    bool Start(uint32_t workThreadCnt = 0);  // 0이면 자동 계산
    void Stop();
    void ProhibitPushAccess();
    void StopAllWorkerThreadAndWait();
    
    // 연결 관리
    bool AddConnectionInfo(int32 dbType, const std::string& host, int port, 
                          const std::string& dbName, const std::string& user, const std::string& password);
    
    // 레거시 호환 인터페이스
    bool AddConnectionInfo(EDataBase dbType, EDBProvider provider, const std::string_view host, 
                          uint32_t port, const std::string_view dbName, const std::string_view user, 
                          const std::string_view password, int32_t initPoolCount);
    NSMySQLConnectionPool* GetDBConnection(int32 dbType);
    void ReconnectConnection(int32 dbType);
    
    // 모니터링
    uint32_t GetDBThreadCount() const { return m_threadCount.value_or(0); }
    int64_t GetQueriesProcessingCount() const { return m_queriesProcessing.load(); }
    int64_t GetDBQueueSize() const;
    std::string GetConnectionPoolCountInfo() const;
    std::string GetConnectionPoolCountLog() const;
    
    // IO 카운트
    int64_t GetInputCount() const { return m_inputCount.load(); }
    int64_t GetOutputCount() const { return m_outputCount.load(); }
    void ResetIOCount() { m_inputCount = 0; m_outputCount = 0; }
    
    // 콜백 설정
    void SetAfterExecuteQuery(std::function<void(const NSQueryData&)> callback) { m_afterExecuteQuery = callback; }
    void SetAfterExecuteQuery(std::function<void(const std::shared_ptr<NSQueryData>&)> callback) { m_afterExecuteQueryShared = callback; }
    
    // 게임 스레드 디스패처 설정
    void SetGameThreadDispatcher(std::function<void(std::function<void()>)> dispatcher) 
    { 
        m_gameThreadPost = dispatcher;
        GameThreadCallback::SetGameThreadDispatcher(dispatcher);
    }

    // 레거시 호환 StartQuery 오버로드들
    
    // 1. Connection과 DataSerializer 받는 버전 (기본)
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        const Connection& connection,
        const NSDataSerializer& serializer)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");

        return StartQueryImpl<SP>(connection, serializer);
    }
    
    // 2. DataSerializer와 샤드키 받는 버전
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        NSDataSerializer& dataSerializer,
        uint64_t shardKey = 0)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        // GameDB 연결 사용
        Connection conn(static_cast<int32>(EDataBase::Game));
        return StartQueryImpl<SP>(conn, dataSerializer);
    }
    
    // 3. 세션만 받는 버전
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        std::shared_ptr<class NSDBSession> session)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        NSDataSerializer serializer;
        uint64_t shardKey = session->GetAID();  // AID로 샤딩
        Connection conn = GetConnectionByAid(shardKey);
        
        return StartQueryImpl<SP>(conn, serializer);
    }
    
    // 4. 세션과 DataSerializer 받는 버전
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQuery(
        std::shared_ptr<class NSDBSession> session,
        NSDataSerializer& dataSerializer)
    {
        static_assert(std::is_base_of_v<NSStoredProcedure, SP>,
            "SP must be derived from NSStoredProcedure");
            
        uint64_t shardKey = session->GetAID();  // AID로 샤딩
        Connection conn = GetConnectionByAid(shardKey);
        
        return StartQueryImpl<SP>(conn, dataSerializer);
    }

    // Cid 기반 연결
    Connection GetConnectionByCid(int32 cid)
    {
        return Connection(static_cast<int32>(EDataBase::Game));
    }

    // Aid 기반 연결
    Connection GetConnectionByAid(int32 aid)
    {
        return Connection(static_cast<int32>(EDataBase::Game));
    }

    // CommonDB 연결
    Connection GetCommonDBConnection()
    {
        return Connection(static_cast<int32>(EDataBase::Common));
    }

    // LogDB 연결
    Connection GetLogDBConnection()
    {
        return Connection(static_cast<int32>(EDataBase::Log));
    }
    
    // Storage Update 관련 메서드 (레거시 호환)
    DBPromise<std::shared_ptr<NSQueryData>> StorageUpdateQuery(
        std::shared_ptr<NSStorageUpdateContainer> containerData,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pQueryFunc,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
        std::shared_ptr<NSDBSession> session = nullptr,
        std::source_location location = std::source_location::current()
    );

    template <typename Sp>
    DBPromise<std::shared_ptr<NSQueryData>> StorageUpdateQueryWithCustomProcedure(
        std::shared_ptr<NSStorageUpdateContainer> containerData,
        NSDataSerializer& dataSerializer,
        std::function<EErrorCode(const std::shared_ptr<NSQueryData>&, const std::shared_ptr<NSStorageUpdateContainer>&)> pResultFunc,
        std::shared_ptr<NSDBSession> session = nullptr,
        std::source_location location = std::source_location::current()
    )
    {
        return DBPromise<std::shared_ptr<NSQueryData>>::Create([=](auto promise) {
            auto queryData = std::make_shared<NSQueryData>(location.function_name(), location.line(), session);
            queryData->GetQueryData() = dataSerializer;

            // CID 기반 스레드 선택
            int threadIndex = containerData->Cid % m_threadCount.value_or(1);
            
            // 작업 큐에 추가
            m_workerManager->QueueWork([=]() mutable {
                try {
                    // SP 타입의 프로시저 실행
                    auto* pool = GetConnectionPool(static_cast<int32>(EDataBase::Game));
                    if (!pool) {
                        promise.SetException(std::make_exception_ptr(
                            std::runtime_error("Connection pool not found")));
                        return;
                    }
                    
                    auto connection = pool->GetConnection();
                    if (!connection) {
                        promise.SetException(std::make_exception_ptr(
                            std::runtime_error("Failed to get connection")));
                        return;
                    }
                    
                    Sp procedure;
                    auto result = procedure.Execute(connection.get(), queryData->GetQueryData());
                    queryData->SetErrorCode(result);
                    
                    // 결과 처리
                    if (pResultFunc) {
                        pResultFunc(queryData, containerData);
                    }
                    
                    promise.SetValue(queryData);
                    pool->ReturnConnection(std::move(connection));
                    
                } catch (...) {
                    promise.SetException(std::current_exception());
                }
            }, threadIndex);
        });
    }

    // 트랜잭션 지원
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQueryWithTransaction(
        const Connection& connection,
        const NSDataSerializer& serializer,
        int64_t transactionId)
    {
        // 트랜잭션 ID를 사용하여 동일 연결 보장
        return StartQueryImpl<SP>(connection, serializer, transactionId);
    }

private:
    // 실제 구현
    template<typename SP>
    DBPromise<std::shared_ptr<NSQueryData>> StartQueryImpl(
        const Connection& connection,
        const NSDataSerializer& serializer,
        int64_t transactionId = 0);

    // 연결 풀 가져오기
    NSMySQLConnectionPool* GetConnectionPool(int32 dbType);
    
    // 샤드키 기반 스레드 선택
    int GetExecutorByShardKey(uint64_t shardKey) const {
        return shardKey % m_threadCount.value_or(1);
    }

    // CID Queue 관리 (CIDQueueManager 통합)
    void EnqueueQuery(int64_t cid, Database::QueryTask task);
    void ProcessCIDQueue(int64_t cid);
    void StartAsyncQuery(const Database::QueryTask& task);
    void PollActiveQueries();
    void ProcessCompletedQuery(Database::AsyncQueryTask& asyncTask);
    void CheckNextTaskForCID(int64_t cid);
    void PostToGameThread(int64_t cid, std::shared_ptr<NSQueryData> queryData);
    
    // 작업 큐 관리 (ThreadedWorkManager 통합)
    void PostWork(std::function<void()> work);
    void WorkerThreadFunc();

private:
    // 연결 풀 배열 (EDataBase::End 크기)
    std::unique_ptr<NSMySQLConnectionPool> m_connectionPools[static_cast<int>(EDataBase::End)];
    
    // CID별 작업 큐 (CIDQueueManager 통합)
    struct CIDQueue {
        mi::queue<Database::QueryTask> tasks;  // mimalloc 사용
        mutable std::mutex mutex;  // CID별 개별 락 (경합 최소화)
        std::atomic<bool> processing{false};  // 현재 처리 중인지 표시
    };
    mi::unordered_map<int64_t, std::shared_ptr<CIDQueue>> m_cidQueues;  // mimalloc 사용
    std::shared_mutex m_cidQueuesMutex;
    
    // 활성 비동기 쿼리 (CIDQueueManager 통합)
    std::vector<Database::AsyncQueryTask> m_activeQueries;
    std::mutex m_activeQueriesMutex;
    std::thread m_pollingThread;
    std::atomic<bool> m_pollingRunning{false};
    
    // 비동기 쿼리 실행기
    std::unique_ptr<Database::AsyncQueryExecutor> m_asyncExecutor;
    
    // 워커 스레드 풀 (ThreadedWorkManager 통합)
    std::vector<std::thread> m_workerThreads;
    std::queue<std::function<void()>> m_workQueue;
    mutable std::mutex m_workQueueMutex;  // const 메서드에서 사용하므로 mutable
    std::condition_variable m_workCV;
    std::atomic<bool> m_running{false};
    
    // 스레드 수
    std::optional<uint32_t> m_threadCount;
    
    // 통계
    std::atomic<int64_t> m_queriesProcessing{0};
    std::atomic<int64_t> m_inputCount{0};
    std::atomic<int64_t> m_outputCount{0};
    
    // 콜백
    std::function<void(const NSQueryData&)> m_afterExecuteQuery;
    std::function<void(const std::shared_ptr<NSQueryData>&)> m_afterExecuteQueryShared;
    
    // 게임 스레드 콜백 (GameThreadCallback 통합)
    using GameThreadPostFunc = std::function<void(std::function<void()>)>;
    GameThreadPostFunc m_gameThreadPost;
    
    // 상태
    std::atomic<bool> m_initialized{false};
    std::atomic<bool> m_pushAccessProhibit{false};
    std::atomic<int32_t> m_pushAccessCount{0};
    
    // 시퀀스 관리 - 스레드 로컬 스토리지 사용 (락 불필요)
    struct ThreadLocalSequence {
        std::unordered_map<int64_t, int64_t> sequenceByCid;  // atomic 불필요
    };
    static thread_local ThreadLocalSequence t_localSequence;
    
public:
    // 시퀀스 관리 메서드 (최소한의 기능만)
    int64_t GetNextStorageSequence(int64_t cid);
    void OnSessionClosed(int64_t cid);
};